<template>
  <UDrawer
    v-model:open="isOpen"
    direction="right"
    :overlay="false"
    class="shadow-xl"
  >
    <template #content>
      <div class="flex flex-col h-full bg-white dark:bg-primary-900">
        <!-- 营销化头部 -->
        <div class="relative overflow-hidden">
          <!-- 背景图片 -->
          <div class="absolute inset-0">
            <img
              src="/img/photos/meeting-2284501_640.jpg"
              alt="AI 保险科技咨询"
              class="w-full h-full object-cover"
            />
          </div>

          <!-- 背景遮罩 -->
          <div
            class="absolute inset-0 bg-gradient-to-br from-primary-500/80 via-primary-600/80 to-primary-700/80"
          ></div>

          <div class="relative p-6">
            <!-- 头部内容 -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex items-center gap-3">
                <div
                  class="w-12 h-12 rounded-xl bg-white/25 backdrop-blur-sm flex items-center justify-center shadow-lg"
                >
                  <UIcon
                    name="i-heroicons-chat-bubble-left-right"
                    class="w-6 h-6 text-white"
                  />
                </div>
                <div>
                  <h3 class="text-xl font-bold text-white drop-shadow-lg">
                    AI 保险科技咨询
                  </h3>
                  <p class="text-sm text-white/90 font-medium">
                    专业顾问 1对1 服务
                  </p>
                </div>
              </div>

              <UButton
                color="neutral"
                variant="ghost"
                size="sm"
                square
                class="text-white hover:bg-white/10 flex items-center justify-center"
                @click="closeDrawer"
              >
                <UIcon name="i-heroicons-x-mark" class="w-5 h-5" />
              </UButton>
            </div>

            <!-- 营销文案和联系方式 -->
            <div class="space-y-4">
              <div class="flex items-center gap-3 text-sm">
                <div
                  class="flex items-center gap-2 bg-white/20 backdrop-blur-sm px-3 py-2 rounded-lg"
                >
                  <UIcon
                    name="i-heroicons-check-circle"
                    class="w-4 h-4 text-green-300"
                  />
                  <span class="text-white font-medium">24小时内专业回复</span>
                </div>
                <div
                  class="flex items-center gap-2 bg-white/20 backdrop-blur-sm px-3 py-2 rounded-lg"
                >
                  <UIcon
                    name="i-heroicons-check-circle"
                    class="w-4 h-4 text-green-300"
                  />
                  <span class="text-white font-medium">免费技术咨询</span>
                </div>
              </div>

              <div class="flex items-center gap-4 text-sm">
                <a
                  href="tel:+86 181 2137 8388"
                  class="flex items-center gap-2 bg-white/20 backdrop-blur-sm px-3 py-2 rounded-lg text-white hover:bg-white/30 transition-all duration-200"
                >
                  <UIcon name="i-heroicons-phone" class="w-4 h-4" />
                  <span class="font-semibold">+86 181 2137 8388</span>
                </a>
                <a
                  href="mailto:<EMAIL>"
                  class="flex items-center gap-2 bg-white/20 backdrop-blur-sm px-3 py-2 rounded-lg text-white hover:bg-white/30 transition-all duration-200"
                >
                  <UIcon name="i-heroicons-envelope" class="w-4 h-4" />
                  <span class="font-semibold"><EMAIL></span>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- 表单内容区域 -->
        <div class="flex-1 overflow-y-auto">
          <!-- 提交成功页面 -->
          <div
            v-if="isSubmitSuccess"
            class="p-6 flex flex-col items-center justify-center h-full"
          >
            <div class="text-center space-y-6 max-w-sm">
              <!-- 成功图标动画 -->
              <div class="relative">
                <div
                  class="w-20 h-20 mx-auto bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg animate-bounce-in"
                >
                  <UIcon
                    name="i-heroicons-check-circle"
                    class="w-12 h-12 text-white animate-scale-in"
                  />
                </div>
              </div>

              <!-- 成功文案 -->
              <div class="space-y-3">
                <h3
                  class="text-2xl font-bold text-primary-900 dark:text-white animate-fade-in-up"
                  style="animation-delay: 0.2s"
                >
                  🎉 提交成功！
                </h3>
                <p
                  class="text-primary-600 dark:text-primary-400 leading-relaxed animate-fade-in-up"
                  style="animation-delay: 0.4s"
                >
                  感谢您的咨询！我们的专业顾问将在
                  <strong class="text-primary-700 dark:text-primary-300"
                    >24小时内</strong
                  >
                  主动联系您，为您提供个性化的 AI 保险科技解决方案。
                </p>
              </div>

              <!-- 后续步骤提示 -->
              <div
                class="bg-gradient-to-r from-primary-50 to-green-50 dark:from-primary-900/30 dark:to-green-900/30 rounded-xl p-4 space-y-3 animate-fade-in-up"
                style="animation-delay: 0.6s"
              >
                <h4
                  class="text-sm font-semibold text-primary-800 dark:text-primary-200 flex items-center gap-2"
                >
                  <UIcon name="i-heroicons-clock" class="w-4 h-4" />
                  接下来会发生什么？
                </h4>
                <div
                  class="space-y-2 text-xs text-primary-600 dark:text-primary-400"
                >
                  <div class="flex items-start gap-2">
                    <div
                      class="w-1.5 h-1.5 bg-primary-400 rounded-full mt-1.5 flex-shrink-0"
                    ></div>
                    <span>专业顾问审核您的需求</span>
                  </div>
                  <div class="flex items-start gap-2">
                    <div
                      class="w-1.5 h-1.5 bg-primary-400 rounded-full mt-1.5 flex-shrink-0"
                    ></div>
                    <span>准备个性化解决方案</span>
                  </div>
                  <div class="flex items-start gap-2">
                    <div
                      class="w-1.5 h-1.5 bg-primary-400 rounded-full mt-1.5 flex-shrink-0"
                    ></div>
                    <span>主动联系您进行深度沟通</span>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div
                class="space-y-3 animate-fade-in-up"
                style="animation-delay: 0.8s"
              >
                <UButton
                  size="lg"
                  class="w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 shadow-lg shadow-primary-500/25 hover:shadow-primary-500/40 transition-all duration-200 rounded-xl flex items-center justify-center"
                  @click="resetToForm"
                >
                  <template #leading>
                    <UIcon name="i-heroicons-plus-circle" class="w-5 h-5" />
                  </template>
                  继续提交咨询
                </UButton>

                <UButton
                  color="neutral"
                  variant="ghost"
                  size="lg"
                  class="w-full flex items-center justify-center"
                  @click="closeDrawer"
                >
                  关闭窗口
                </UButton>
              </div>

              <!-- 紧急联系方式 -->
              <div
                class="pt-4 border-t border-primary-100 dark:border-primary-800 animate-fade-in-up"
                style="animation-delay: 1s"
              >
                <p class="text-xs text-primary-500 dark:text-primary-400 mb-3">
                  如有紧急需求，您也可以直接联系我们：
                </p>
                <div class="flex items-center justify-center gap-4 text-xs">
                  <a
                    href="tel:+86 181 2137 8388"
                    class="flex items-center gap-1.5 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                  >
                    <UIcon name="i-heroicons-phone" class="w-3 h-3" />
                    <span>+86 181 2137 8388</span>
                  </a>
                  <a
                    href="mailto:<EMAIL>"
                    class="flex items-center gap-1.5 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                  >
                    <UIcon name="i-heroicons-envelope" class="w-3 h-3" />
                    <span><EMAIL></span>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- 原有表单内容 -->
          <div v-else class="p-6 space-y-6">
            <!-- 营销引导文案 -->
            <div class="text-center space-y-2">
              <h4
                class="text-lg font-semibold text-primary-900 dark:text-white"
              >
                🚀 开启 AI 保险科技之旅
              </h4>
              <p class="text-sm text-primary-600 dark:text-primary-400">
                填写下方信息，我们将安排专业顾问为您提供个性化解决方案
              </p>
            </div>

            <UForm
              ref="form"
              :schema="schema"
              :state="state"
              class="space-y-5"
              @submit="onSubmit"
              @error="onError"
            >
              <!-- 表单字段 -->
              <UFormField label="姓名" name="name" required>
                <UInput
                  v-model="state.name"
                  placeholder="请输入您的姓名"
                  icon="i-heroicons-user"
                  size="lg"
                  class="rounded-xl w-full"
                />
              </UFormField>

              <UFormField label="手机号" name="phone" required>
                <UInput
                  v-model="state.phone"
                  placeholder="请输入您的手机号"
                  icon="i-heroicons-phone"
                  size="lg"
                  class="rounded-xl w-full"
                />
              </UFormField>

              <UFormField label="邮箱" name="email" required>
                <UInput
                  v-model="state.email"
                  type="email"
                  placeholder="请输入您的邮箱地址"
                  icon="i-heroicons-envelope"
                  size="lg"
                  class="rounded-xl w-full"
                />
              </UFormField>

              <UFormField label="公司名称" name="company" required>
                <UInput
                  v-model="state.company"
                  placeholder="请输入您的公司名称"
                  icon="i-heroicons-building-office"
                  size="lg"
                  class="rounded-xl w-full"
                />
              </UFormField>

              <UFormField label="部门" name="department">
                <UInput
                  v-model="state.department"
                  placeholder="请输入您的部门（可选）"
                  icon="i-heroicons-user-group"
                  size="lg"
                  class="rounded-xl w-full"
                />
              </UFormField>

              <UFormField label="咨询内容" name="content" required>
                <UTextarea
                  v-model="state.content"
                  placeholder="请详细描述您的咨询需求，例如：&#10;• 感兴趣的产品或解决方案&#10;• 预期解决的业务问题&#10;• 项目规模和时间计划&#10;• 预算范围和其他要求"
                  :rows="5"
                  resize
                  size="lg"
                  class="rounded-xl w-full"
                />
              </UFormField>

              <!-- 提交按钮 -->
              <div class="pt-2">
                <UButton
                  type="submit"
                  size="xl"
                  :loading="isSubmitting"
                  :disabled="isSubmitting"
                  class="w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 shadow-lg shadow-primary-500/25 hover:shadow-primary-500/40 transition-all duration-200 rounded-xl flex items-center justify-center text-center"
                >
                  <template #leading v-if="!isSubmitting">
                    <UIcon name="i-heroicons-paper-airplane" class="w-5 h-5" />
                  </template>
                  <template #leading v-else>
                    <UIcon
                      name="i-heroicons-arrow-path"
                      class="w-5 h-5 animate-spin"
                    />
                  </template>
                  {{ isSubmitting ? "提交中..." : "立即获取 AI 解决方案" }}
                </UButton>
              </div>
            </UForm>
          </div>
        </div>

        <!-- 底部信任标识 -->
        <div class="border-t border-primary-100">
          <div class="p-4 text-center">
            <div
              class="flex items-center justify-center gap-4 text-xs text-primary-500 dark:text-primary-400"
            >
              <div class="flex items-center gap-1">
                <UIcon name="i-heroicons-shield-check" class="w-3 h-3" />
                <span>数据安全</span>
              </div>
              <div class="flex items-center gap-1">
                <UIcon name="i-heroicons-clock" class="w-3 h-3" />
                <span>24h 响应</span>
              </div>
              <div class="flex items-center gap-1">
                <UIcon name="i-heroicons-star" class="w-3 h-3" />
                <span>专业服务</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </UDrawer>
</template>

<script setup lang="ts">
import { z } from "zod";
import type { FormSubmitEvent } from "@nuxt/ui";

// 定义接口
interface InquiryForm {
  name: string;
  phone: string;
  email: string;
  company: string;
  department?: string;
  content: string;
}

// 组件属性
interface Props {
  modelValue?: boolean;
}

// 组件事件
interface Emits {
  (event: "update:modelValue", value: boolean): void;
  (event: "submit", data: InquiryForm): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
});

const emit = defineEmits<Emits>();

// 响应式状态
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const isSubmitting = ref(false);
const isSubmitSuccess = ref(false);

// 表单状态
const state = reactive<InquiryForm>({
  name: "",
  phone: "",
  email: "",
  company: "",
  department: "",
  content: "",
});

// 表单验证规则
const schema = z.object({
  name: z
    .string()
    .min(1, "请输入您的姓名")
    .min(2, "姓名至少需要2个字符")
    .max(20, "姓名不能超过20个字符"),
  phone: z
    .string()
    .min(1, "请输入您的手机号")
    .regex(/^1[3-9]\d{9}$/, "请输入正确的手机号格式"),
  email: z.string().min(1, "请输入您的邮箱地址").email("请输入正确的邮箱格式"),
  company: z
    .string()
    .min(1, "请输入您的公司名称")
    .min(2, "公司名称至少需要2个字符")
    .max(100, "公司名称不能超过100个字符"),
  department: z.string().max(50, "部门名称不能超过50个字符").optional(),
  content: z
    .string()
    .min(1, "请输入咨询内容")
    .min(10, "咨询内容至少需要10个字符")
    .max(1000, "咨询内容不能超过1000个字符"),
});

// 表单引用
const form = ref();

// 关闭抽屉
const closeDrawer = () => {
  isOpen.value = false;
};

// 重置表单
const resetForm = () => {
  state.name = "";
  state.phone = "";
  state.email = "";
  state.company = "";
  state.department = "";
  state.content = "";
  form.value?.clear();
};

// 重置到表单状态
const resetToForm = () => {
  isSubmitSuccess.value = false;
  resetForm();
};

// 表单提交处理
const onSubmit = async (event: FormSubmitEvent<InquiryForm>) => {
  isSubmitting.value = true;

  try {
    // 调用邮件发送 API
    const response = await $fetch("/api/mail", {
      method: "POST",
      body: {
        name: event.data.name,
        phone: event.data.phone,
        email: event.data.email,
        company: event.data.company,
        department: event.data.department,
        content: event.data.content,
      },
    });

    // 触发提交事件
    emit("submit", event.data);

    // 显示成功页面
    isSubmitSuccess.value = true;

    console.log("咨询表单提交成功:", event.data);
    console.log("邮件发送响应:", response);
  } catch (error) {
    console.error("提交失败:", error);

    // 显示错误消息
    const toast = useToast();
    toast.add({
      title: "提交失败",
      description: "提交过程中出现错误，请稍后重试或直接联系我们",
      icon: "i-heroicons-exclamation-triangle",
      color: "error",
    });
  } finally {
    isSubmitting.value = false;
  }
};

// 表单验证错误处理
const onError = (event: any) => {
  const element = document.getElementById(event.errors[0].path);
  element?.focus();
  element?.scrollIntoView({ behavior: "smooth", block: "center" });
};

// 监听抽屉关闭，重置所有状态
watch(isOpen, (newValue) => {
  if (!newValue) {
    // 延迟重置，确保动画完成
    setTimeout(() => {
      isSubmitSuccess.value = false;
      resetForm();
    }, 300);
  }
});

// 暴露方法给父组件
defineExpose({
  open: () => {
    isOpen.value = true;
  },
  close: closeDrawer,
  reset: resetForm,
});
</script>

<style scoped>
/* 现代化滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background-color: var(--color-primary-50);
  border-radius: 3px;
}

.dark .overflow-y-auto::-webkit-scrollbar-track {
  background-color: var(--color-primary-900);
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: var(--color-primary-300);
  border-radius: 3px;
  transition: all 0.2s ease;
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: var(--color-primary-700);
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-primary-400);
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-primary-600);
}

/* 卡片进入动画 */
.space-y-6 > * {
  animation: slideInUp 0.4s ease-out;
  animation-fill-mode: both;
}

.space-y-6 > *:nth-child(1) {
  animation-delay: 0.1s;
}
.space-y-6 > *:nth-child(2) {
  animation-delay: 0.2s;
}
.space-y-6 > *:nth-child(3) {
  animation-delay: 0.3s;
}
.space-y-6 > *:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮悬停效果增强 */
:deep(.ui-button) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ui-button:hover) {
  transform: translateY(-1px);
}

:deep(.ui-button:active) {
  transform: translateY(0);
}

/* 确保按钮内容居中 */
:deep(.ui-button .ui-button-content) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

:deep(.ui-button .ui-button-leading) {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 输入框聚焦效果 */
:deep(.ui-input:focus-within),
:deep(.ui-textarea:focus-within) {
  transform: scale(1.01);
  transition: transform 0.2s ease;
}

/* 表单字段聚焦效果 */
:deep(.ui-form-field) {
  transition: all 0.2s ease;
}

:deep(.ui-form-field:focus-within) {
  transform: translateY(-1px);
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 微妙的背景纹理 */
.texture-bg {
  background-image: radial-gradient(
    circle at 1px 1px,
    rgba(255, 255, 255, 0.15) 1px,
    transparent 0
  );
  background-size: 20px 20px;
}

/* 成功页面动画 */
@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scale-in {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.4s ease-out 0.2s forwards;
  transform: scale(0);
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

/* 粒子效果优化 */
.animate-ping {
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* 响应式优化 */
@media (max-width: 640px) {
  .space-y-6 > * {
    animation-delay: 0s !important;
  }

  .animate-fade-in-up {
    animation-delay: 0s !important;
  }
}
</style>
