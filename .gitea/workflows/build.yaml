name: Build and Deploy

on:
  push:
    branches:
      - master
      - develop
  pull_request:
    branches:
      - master
  workflow_dispatch:

permissions:
  contents: read
  packages: write

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          registry: git.yingrtech.com
          username: ${{ secrets.PACKAGE_USERNAME }}
          password: ${{ secrets.PACKAGE_PASSWORD }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            network=host

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: true
          add-hosts: |
            git.yingrtech.com:*************
          tags: |
            git.yingrtech.com/yingrtech/official-web/yingrt:${{ github.sha }}
            git.yingrtech.com/yingrtech/official-web/yingrt:latest

      - name: Export environment variables for staging
        if: github.ref == 'refs/heads/develop'
        run: |
          echo "SSH_HOST=${{ vars.SSH_STAGING_HOST }}" >> $GITHUB_ENV
          echo "SSH_USER=${{ vars.SSH_STAGING_USER }}" >> $GITHUB_ENV

      - name: Export environment variables for production
        if: github.ref == 'refs/heads/main'
        run: |
          echo "SSH_HOST=${{ vars.SSH_PROD_HOST }}" >> $GITHUB_ENV
          echo "SSH_USER=${{ vars.SSH_PROD_USER }}" >> $GITHUB_ENV

      - name: Setup project
        env:
          SSH_HOST: ${{ env.SSH_HOST }}
          SSH_USER: ${{ env.SSH_USER }}
          APP_PATH: ${{ env.APP_PATH }}
        run: |
          if [ "$GITHUB_REF" == "refs/heads/main" ]; then
            SSH_PRIVATE_KEY="${{ secrets.SSH_PROD_PRIVATE_KEY }}"
          else
            SSH_PRIVATE_KEY="${{ secrets.SSH_STAGING_PRIVATE_KEY }}"
          fi

          mkdir -p "$HOME/.ssh"
          echo "$SSH_PRIVATE_KEY" > "$HOME/.ssh/id_rsa"
          chmod 600 "$HOME/.ssh/id_rsa"

          ssh -i "$HOME/.ssh/id_rsa" -o StrictHostKeyChecking=no "${SSH_USER}@${SSH_HOST}" << EOF
            set -xe

            docker login -u "${{ secrets.PACKAGE_USERNAME }}" -p "${{ secrets.PACKAGE_PASSWORD }}" git.yingrtech.com

            docker pull "git.yingrtech.com/yingrtech/official-web/yingrt:${GITHUB_SHA}"
            docker rm -f official-web-yingrt
            docker run -d --name official-web-yingrt --restart=unless-stopped --network=app -p 3000:3000 git.yingrtech.com/yingrtech/official-web/yingrt:${GITHUB_SHA}
          EOF
